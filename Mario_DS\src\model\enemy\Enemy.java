package model.enemy;

import java.awt.image.BufferedImage;
import java.util.concurrent.atomic.AtomicInteger;

import model.GameObject;

public abstract class Enemy extends GameObject {

  private static final AtomicInteger nextId = new AtomicInteger(0);
  private final int uniqueId;

  public Enemy(double x, double y, BufferedImage style) {
    super(x, y, style);
    setFalling(false);
    setJumping(false);
    this.uniqueId = nextId.getAndIncrement();
  }

  /**
   * Constructor that allows setting a specific unique ID (for map loading
   * consistency)
   */
  public Enemy(double x, double y, BufferedImage style, int uniqueId) {
    super(x, y, style);
    setFalling(false);
    setJumping(false);
    this.uniqueId = uniqueId;
    // Update the next ID counter to ensure no conflicts
    nextId.updateAndGet(current -> Math.max(current, uniqueId + 1));
  }

  /**
   * Get the unique ID of this enemy
   */
  public int getUniqueId() {
    return uniqueId;
  }

  /**
   * Reset the ID counter (used for testing or when loading a new map)
   */
  public static void resetIdCounter() {
    nextId.set(0);
  }

  /**
   * Get the current next ID value (for debugging)
   */
  public static int getNextId() {
    return nextId.get();
  }
}
