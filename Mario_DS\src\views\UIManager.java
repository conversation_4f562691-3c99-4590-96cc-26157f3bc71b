package views;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.FontFormatException;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Point;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import javax.swing.JPanel;

import manager.GameEngine;
import manager.GameMode;
import manager.GameStatus;
import manager.ScoreManager.MarioScore;
import manager.ScoreManager.ScoreRepository;

public class UIManager extends JPanel {

  private GameEngine engine;
  private Font gameFont;
  private BufferedImage logoImage, aboutScreenImage, helpScreenImage, gameOverScreen, gameScore;
  private BufferedImage heartIcon;
  private BufferedImage coinIcon;
  private BufferedImage selectIcon;
  private MapSelection mapSelection;

  // Score caching to avoid continuous database connections
  private List<MarioScore> cachedTopScores = new ArrayList<>();
  private MarioScore cachedRecentPlay = new MarioScore(0, 0, "2000-01-01T00:00:00");
  private boolean scoresLoaded = false;
  private boolean scoreLoadError = false;
  private String errorMessage = "";

  public UIManager(GameEngine engine, int width, int height) {
    setPreferredSize(new Dimension(width, height));
    setMaximumSize(new Dimension(width, height));
    setMinimumSize(new Dimension(width, height));

    this.engine = engine;
    ImageLoader loader = engine.getImageLoader();

    mapSelection = new MapSelection();

    BufferedImage sprite = loader.loadImage("/sprite.png");
    this.heartIcon = loader.loadImage("/heart-icon.png");
    this.coinIcon = loader.getSubImage(sprite, 1, 5, 48, 48);
    this.selectIcon = loader.loadImage("/select-icon.png");
    this.logoImage = loader.loadImage("/logo.png");

    this.helpScreenImage = loader.loadImage("/help-screen.png");
    this.aboutScreenImage = loader.loadImage("/about-screen.png");
    this.gameOverScreen = loader.loadImage("/game-over.png");
    this.gameScore = loader.loadImage("/game-score.jpg");

    try {
      InputStream in = getClass().getResourceAsStream("/media/font/mario-font.ttf");
      gameFont = Font.createFont(Font.TRUETYPE_FONT, in);
    } catch (FontFormatException | IOException e) {
      gameFont = new Font("Verdana", Font.PLAIN, 12);
      e.printStackTrace();
    }
  }

  @Override
  public void paintComponent(Graphics g) {
    super.paintComponent(g);

    Graphics2D g2 = (Graphics2D) g.create();
    GameStatus gameStatus = engine.getGameStatus();

    if (gameStatus == GameStatus.START_SCREEN) {
      drawStartScreen(g2);
    } else if (gameStatus == GameStatus.MAP_SELECTION) {
      drawMapSelectionScreen(g2);
    } else if (gameStatus == GameStatus.ABOUT_SCREEN) {
      drawAboutScreen(g2);
    } else if (gameStatus == GameStatus.HELP_SCREEN) {
      drawHelpScreen(g2);
    } else if (gameStatus == GameStatus.GAME_OVER) {
      drawGameOverScreen(g2);
    } else if (gameStatus == GameStatus.RECONNECTING) {
      drawReconnectingScreen(g2);
    } else if (gameStatus == GameStatus.WAITING_FOR_SERVER) {
      drawWaitingForServerScreen(g2);
    } else if (gameStatus == GameStatus.MULTIPLAYER_MODE_SELECTION) {
      drawMultiplayerModeSelectionScreen(g2);
    } else if (gameStatus == GameStatus.GAME_SCORE) {
      // Load scores only once when entering the score screen
      if (!scoresLoaded && !scoreLoadError) {
        loadScoresFromDatabase();
      }

      // Display cached scores or error message
      if (scoreLoadError) {
        g2.setFont(gameFont.deriveFont(24f));
        g2.setColor(Color.WHITE);
        g2.drawString("Error loading scores from database", 200, 300);
        g2.drawString(errorMessage, 200, 350);
      } else {
        drawScoreList(g2, cachedTopScores, cachedRecentPlay);
      }

    } else if (gameStatus == GameStatus.RUNNING) {
      Point camLocation = engine.getCameraLocation();
      g2.translate(-camLocation.x, -camLocation.y);
      engine.drawMap(g2);
      drawPlayerLabels(g2); // Draw player labels in game world coordinates
      g2.translate(camLocation.x, camLocation.y);

      drawLeftInfo(g2);
      drawAcquiredCoins(g2);
      // drawRemainingTime(g2);

      if (gameStatus == GameStatus.PAUSED) {
        drawPauseScreen(g2);
      } else if (gameStatus == GameStatus.MISSION_PASSED) {
        drawVictoryScreen(g2);
      }
    }

    g2.dispose();
  }

  private void drawVictoryScreen(Graphics2D g2) {
    g2.setFont(gameFont.deriveFont(50f));
    g2.setColor(Color.WHITE);
    String displayedStr = "YOU WON!";
    int stringLength = g2.getFontMetrics().stringWidth(displayedStr);
    g2.drawString(displayedStr, (getWidth() - stringLength) / 2, getHeight() / 2);
  }

  private void drawReconnectingScreen(Graphics2D g2) {
    // Set background color
    g2.setColor(Color.BLACK);
    g2.fillRect(0, 0, getWidth(), getHeight());

    g2.setFont(gameFont.deriveFont(50f));
    g2.setColor(Color.WHITE);
    String displayedStr = "Reconnecting...";
    int stringLength = g2.getFontMetrics().stringWidth(displayedStr);
    g2.drawString(displayedStr, (getWidth() - stringLength) / 2, getHeight() / 2);
  }

  private void drawWaitingForServerScreen(Graphics2D g2) {
    // Set background color
    g2.setColor(Color.BLACK);
    g2.fillRect(0, 0, getWidth(), getHeight());

    var multiplayerManager = engine.getMultiplayerManager();
    var currentMode = multiplayerManager.getCurrentMode();

    // Draw title
    g2.setFont(gameFont.deriveFont(48f));
    g2.setColor(Color.WHITE);
    String title = currentMode == GameMode.NETWORK_HOST ? "HOSTING GAME" : "CONNECTING TO HOST";
    int titleWidth = g2.getFontMetrics().stringWidth(title);
    g2.drawString(title, (getWidth() - titleWidth) / 2, 150);

    // Draw status information
    g2.setFont(gameFont.deriveFont(24f));
    int startY = 250;
    int lineHeight = 40;
    int currentY = startY;

    // Connection status
    String connectionStatus = multiplayerManager.getCurrentModeDisplayName();
    int statusWidth = g2.getFontMetrics().stringWidth(connectionStatus);
    g2.setColor(multiplayerManager.isConnected() ? Color.GREEN : Color.YELLOW);
    g2.drawString(connectionStatus, (getWidth() - statusWidth) / 2, currentY);
    currentY += lineHeight;

    // Host information (if hosting)
    if (currentMode == GameMode.NETWORK_HOST) {
      String hostInfo = multiplayerManager.getHostInfo();
      if (!hostInfo.isEmpty()) {
        String[] lines = hostInfo.split("\n");
        g2.setColor(Color.CYAN);
        for (String line : lines) {
          int lineWidth = g2.getFontMetrics().stringWidth(line);
          g2.drawString(line, (getWidth() - lineWidth) / 2, currentY);
          currentY += lineHeight;
        }
      }
    }

    // Control instructions
    currentY += 20;
    String controls = multiplayerManager.getControlInstructions();
    g2.setColor(Color.WHITE);
    int controlsWidth = g2.getFontMetrics().stringWidth(controls);
    g2.drawString(controls, (getWidth() - controlsWidth) / 2, currentY);

    // Instructions
    currentY += 80;
    g2.setFont(gameFont.deriveFont(18f));
    g2.setColor(Color.LIGHT_GRAY);
    String instruction = "Press ESC to return to menu";
    int instructionWidth = g2.getFontMetrics().stringWidth(instruction);
    g2.drawString(instruction, (getWidth() - instructionWidth) / 2, currentY);
  }

  private void drawHelpScreen(Graphics2D g2) {
    g2.drawImage(helpScreenImage, 0, 0, this.getWidth(), this.getHeight(), null);
  }

  private void drawAboutScreen(Graphics2D g2) {
    g2.drawImage(aboutScreenImage, 0, 0, this.getWidth(), this.getHeight(), null);
  }

  private void drawGameOverScreen(Graphics2D g2) {
    g2.drawImage(gameOverScreen, 0, 0, this.getWidth(), this.getHeight(), null);
    g2.setFont(gameFont.deriveFont(50f));
    g2.setColor(new Color(130, 48, 48));

    String acquiredPoints = "Mario: " + engine.getScore() + " Mario2: " + engine.getScore2();
    int stringLength = g2.getFontMetrics().stringWidth(acquiredPoints);
    int stringHeight = g2.getFontMetrics().getHeight();

    g2.drawString(acquiredPoints, (getWidth() - stringLength) / 2, getHeight() - stringHeight * 2);
  }

  private void drawPauseScreen(Graphics2D g2) {
    g2.setFont(gameFont.deriveFont(50f));
    g2.setColor(Color.WHITE);
    String displayedStr = "PAUSED";
    int stringLength = g2.getFontMetrics().stringWidth(displayedStr);
    g2.drawString(displayedStr, (getWidth() - stringLength) / 2, getHeight() / 2);
  }

  private void drawAcquiredCoins(Graphics2D g2) {
    g2.setFont(gameFont.deriveFont(24f));
    g2.setColor(Color.WHITE);

    // Standard UI positioning - align with other elements
    int topMargin = 15;
    int textY = 35;
    int coinSize = 25;

    String displayedStr = "" + engine.getCoins();

    // Position coin icon and text on the right side, properly aligned
    int coinX = getWidth() - 80;
    int textX = getWidth() - 50;

    g2.drawImage(coinIcon, coinX, topMargin, coinSize, coinSize, null);
    g2.drawString(displayedStr, textX, textY);
  }

  private void drawLeftInfo(Graphics2D g2) {
    boolean isMultiplayer = engine.getMultiplayerManager().getCurrentMode().isMultiplayer();

    g2.setFont(gameFont.deriveFont(24f));
    g2.setColor(Color.WHITE);

    int topMargin = 10;
    int textY = 35;
    int heartSize = 25;

    if (isMultiplayer) {
      int mario1Lives = engine.getMapManager().getRemainingLives("mario");
      int mario2Lives = engine.getMapManager().getRemainingLives("mario2");

      g2.drawImage(heartIcon, 20, topMargin, heartSize, heartSize, null);
      g2.setColor(Color.CYAN);
      g2.drawString("P1:" + mario1Lives, 50, textY);

      g2.drawImage(heartIcon, 160, topMargin, heartSize, heartSize, null);
      g2.setColor(Color.YELLOW);
      g2.drawString("P2:" + mario2Lives, 200, textY);
    } else {
      String displayedStr = "" + engine.getRemainingLives();

      g2.drawImage(heartIcon, 20, topMargin, heartSize, heartSize, null);
      g2.setColor(Color.WHITE);
      g2.drawString(displayedStr, 50, textY);
    }

    g2.setFont(gameFont.deriveFont(24f));
    g2.setColor(Color.WHITE);

    String scoreString = "MARIO: " + engine.getScore();
    scoreString += " MARIO2: " + engine.getScore2();
    scoreString += " TIME: " + engine.getRemainingTime();

    var textX = isMultiplayer ? 320 : 140;

    g2.drawString(scoreString, textX, textY);

    // Draw network status for network multiplayer modes
    if (engine.getMultiplayerManager().getCurrentMode().isNetworkMode()) {
      drawNetworkStatus(g2);
    }
  }

  /**
   * Draw network connection status and sync information
   */
  private void drawNetworkStatus(Graphics2D g2) {
    var multiplayerManager = engine.getMultiplayerManager();

    g2.setFont(gameFont.deriveFont(16f));
    int statusY = 70; // Below the main UI elements

    // Connection status
    if (multiplayerManager.isConnected()) {
      g2.setColor(Color.GREEN);
      String status = multiplayerManager.isHost() ? "HOST" : "CLIENT";
      g2.drawString(status, 20, statusY);
    } else {
      g2.setColor(Color.RED);
      g2.drawString("DISCONNECTED", 20, statusY);
    }

    // Sync status (if we have game state manager)
    if (engine.getGameStateManager() != null) {
      String syncStatus = engine.getGameStateManager().getSyncStatus();
      g2.setColor(engine.getGameStateManager().isSynchronized() ? Color.GREEN : Color.ORANGE);
      g2.drawString(syncStatus, 120, statusY);
    }
  }

  /**
   * Draw player identification labels above Mario characters in multiplayer
   * mode This method is called in game world coordinates (after camera
   * translation)
   */
  private void drawPlayerLabels(Graphics2D g2) {
    boolean isMultiplayer = engine.getMultiplayerManager().getCurrentMode().isMultiplayer();
    if (!isMultiplayer) {
      return; // Don't draw labels in single player mode
    }

    try {
      // Get Mario positions from the map manager
      var mario1 = engine.getMapManager().getMario("mario");
      var mario2 = engine.getMapManager().getMario("mario2");

      if (mario1 != null && mario2 != null) {
        g2.setFont(gameFont.deriveFont(16f));

        // Draw Player 1 label
        g2.setColor(Color.CYAN);
        String p1Label = "P1";
        int p1LabelWidth = g2.getFontMetrics().stringWidth(p1Label);
        g2.drawString(p1Label, (int) mario1.getX() + 24 - p1LabelWidth / 2, (int) mario1.getY() - 10);

        // Draw Player 2 label
        g2.setColor(Color.YELLOW);
        String p2Label = "P2";
        int p2LabelWidth = g2.getFontMetrics().stringWidth(p2Label);
        g2.drawString(p2Label, (int) mario2.getX() + 24 - p2LabelWidth / 2, (int) mario2.getY() - 10);

        // Optional: Draw connection line when players are far apart
        double distance = Math.abs(mario1.getX() - mario2.getX());
        if (distance > 300) { // Only show when players are far apart
          g2.setColor(new Color(255, 255, 255, 100)); // Semi-transparent white
          g2.drawLine((int) mario1.getX() + 24, (int) mario1.getY() + 24, (int) mario2.getX() + 24,
              (int) mario2.getY() + 24);
        }
      }
    } catch (Exception e) {
      // Silently handle any exceptions to prevent game crashes
      System.err.println("Error drawing player labels: " + e.getMessage());
    }
  }

  // Draw the start screen with logo and all 5 menu options
  private void drawStartScreen(Graphics2D g2) {
    int selectedRow = engine.getStartScreenSelection().getLineNumber();

    // Set black background
    g2.setColor(Color.BLACK);
    g2.fillRect(0, 0, getWidth(), getHeight());

    // Draw logo at the top with responsive sizing
    if (logoImage != null) {
      int originalLogoWidth = logoImage.getWidth();
      int originalLogoHeight = logoImage.getHeight();

      // Calculate responsive logo size based on screen dimensions
      // Logo should take up at most 40% of screen width and 25% of screen
      // height
      double maxWidthRatio = 0.6;
      double maxHeightRatio = 0.35;

      int maxLogoWidth = (int) (getWidth() * maxWidthRatio);
      int maxLogoHeight = (int) (getHeight() * maxHeightRatio);

      // Calculate scale factor to maintain aspect ratio
      double scaleX = (double) maxLogoWidth / originalLogoWidth;
      double scaleY = (double) maxLogoHeight / originalLogoHeight;
      double scale = Math.min(scaleX, scaleY); // Use smaller scale to fit
                                               // within bounds

      // Ensure minimum scale to keep logo readable
      scale = Math.max(scale, 0.3);

      int logoWidth = (int) (originalLogoWidth * scale);
      int logoHeight = (int) (originalLogoHeight * scale);
      int logoX = (getWidth() - logoWidth) / 2;
      int logoY = Math.max(20, (int) (getHeight() * 0.08)); // Minimum 20px from
                                                            // top, or 8% of
                                                            // screen height

      g2.drawImage(logoImage, logoX, logoY, logoWidth, logoHeight, null);
    }

    // Define menu options text
    String[] menuOptions = { "START GAME", "MULTIPLAYER", "VIEW HELP", "VIEW ABOUT", "GAME SCORE" };

    // Set font and calculate positioning
    g2.setFont(gameFont.deriveFont(36f));
    int startY = getHeight() / 2;
    int optionSpacing = 60;
    int menuX = getWidth() / 2;

    // Draw all menu options
    for (int i = 0; i < menuOptions.length; i++) {
      int yPosition = startY + (i * optionSpacing);

      // Highlight selected option
      if (i == selectedRow) {
        g2.setColor(Color.YELLOW);
        // Draw selection icon
        g2.drawImage(selectIcon, getWidth() / 4, yPosition - selectIcon.getHeight() / 2, null);
      } else {
        g2.setColor(Color.WHITE);
      }

      // Center the text
      int textWidth = g2.getFontMetrics().stringWidth(menuOptions[i]);
      g2.drawString(menuOptions[i], menuX - textWidth / 2, yPosition);
    }

    // Draw instructions at bottom
    g2.setFont(gameFont.deriveFont(20f));
    g2.setColor(Color.GRAY);
    String instruction = "Use UP/DOWN arrows to navigate, ENTER to select, ESC to exit";
    int instWidth = g2.getFontMetrics().stringWidth(instruction);
    g2.drawString(instruction, (getWidth() - instWidth) / 2, getHeight() - 50);
  }

  private void drawMapSelectionScreen(Graphics2D g2) {
    g2.setFont(gameFont.deriveFont(50f));
    g2.setColor(Color.WHITE);
    mapSelection.draw(g2, () -> this.getWidth(), () -> this.getHeight());

    int row = engine.getSelectedMap();
    int y_location = row * 100 + 300 - selectIcon.getHeight();

    g2.drawImage(selectIcon, this.getWidth() / 4, y_location, null);
  }

  private void drawMultiplayerModeSelectionScreen(Graphics2D g2) {
    // Set background color
    g2.setColor(Color.BLACK);
    g2.fillRect(0, 0, getWidth(), getHeight());

    // Draw title
    g2.setFont(gameFont.deriveFont(48f));
    g2.setColor(Color.WHITE);
    String title = "MULTIPLAYER MODE";
    int titleWidth = g2.getFontMetrics().stringWidth(title);
    g2.drawString(title, (getWidth() - titleWidth) / 2, 150);

    // Draw options
    g2.setFont(gameFont.deriveFont(32f));
    int startY = 250;
    int spacing = 80;

    String[] options = { "Local Multiplayer", "Network Host", "Network Client" };

    int selectedOption = engine.getMultiplayerManager().getSelectedMultiplayerOption();

    for (int i = 0; i < options.length; i++) {
      if (i == selectedOption) {
        g2.setColor(Color.YELLOW);
        g2.drawImage(selectIcon, getWidth() / 4, startY + i * spacing - 40, null);
      } else {
        g2.setColor(Color.WHITE);
      }

      int optionWidth = g2.getFontMetrics().stringWidth(options[i]);
      g2.drawString(options[i], (getWidth() - optionWidth) / 2, startY + i * spacing);
    }

    // Draw control instructions for selected option
    g2.setFont(gameFont.deriveFont(20f));
    g2.setColor(Color.CYAN);
    String controlInfo = "";
    switch (selectedOption) {
    case 0:
      controlInfo = "Player 1: WASD + Space | Player 2: Arrow Keys + P";
      break;
    case 1:
      controlInfo = "Host controls: WASD + Space (Others connect to your IP)";
      break;
    case 2:
      controlInfo = "Client controls: Arrow Keys + P (Connect to host IP)";
      break;
    }
    int controlWidth = g2.getFontMetrics().stringWidth(controlInfo);
    g2.drawString(controlInfo, (getWidth() - controlWidth) / 2, startY + options.length * spacing + 40);

    // Draw instructions
    g2.setFont(gameFont.deriveFont(24f));
    g2.setColor(Color.YELLOW);
    String instruction1 = "Press ENTER to select";
    String instruction2 = "Press ESC to go back";
    int inst1Width = g2.getFontMetrics().stringWidth(instruction1);
    int inst2Width = g2.getFontMetrics().stringWidth(instruction2);
    g2.drawString(instruction1, (getWidth() - inst1Width) / 2, getHeight() - 120);
    g2.drawString(instruction2, (getWidth() - inst2Width) / 2, getHeight() - 80);

    // Display current mode info
    g2.setFont(gameFont.deriveFont(20f));
    g2.setColor(Color.CYAN);

    String currentMode = "Current Mode: " + engine.getMultiplayerManager().getCurrentModeDisplayName();
    int modeWidth = g2.getFontMetrics().stringWidth(currentMode);
    g2.drawString(currentMode, (getWidth() - modeWidth) / 2, getHeight() - 40);
  }

  private void drawScoreList(Graphics2D g2, List<MarioScore> scores, MarioScore recentScore) {
    g2.drawImage(gameScore, 0, 0, this.getWidth(), this.getHeight(), null);
    g2.setFont(gameFont.deriveFont(24f)); // Set the font size
    g2.setColor(Color.WHITE); // Set the color

    int x = 200; // X position to start drawing scores
    int y = 200; // Starting Y position for the first score
    int yDelta = 30; // Distance between scores

    if (scores.size() == 0) {
      g2.drawString("No score", (this.getWidth() / 2) - ("No score").length() * 16, y);
      return;
    }

    var top20 = scores.subList(0, scores.size() > 20 ? 20 : scores.size());

    g2.drawString("Leaderboard", (this.getWidth() / 2) - ("Leaderboard").length() * 16, 150);
    for (MarioScore score : top20) {
      g2.drawString(String.format("%-2d", top20.indexOf(score) + 1) + " - " + score.toString(), x, y);
      y += yDelta; // Move down to the next line
    }

    g2.drawString("Recent: " + recentScore.toString(), x, this.getHeight() - 300);
  }

  /**
   * Load scores from database only once to avoid continuous database
   * connections
   */
  private void loadScoresFromDatabase() {
    try {
      ScoreRepository scoreRepository = new ScoreRepository();
      List<MarioScore> allScores = scoreRepository.getAllScores();

      // Get recent play (most recent score by date)
      cachedRecentPlay = allScores.size() > 0 ? allScores.get(0) : new MarioScore(0, 0, "2000-01-01T00:00:00");

      // Get top scores sorted by total score
      cachedTopScores = scoreRepository.getTopScores(20);

      scoresLoaded = true;
      scoreLoadError = false;
      System.out.println("Scores loaded successfully from database");
    } catch (Exception e) {
      scoreLoadError = true;
      errorMessage = "Database connection failed";
      System.err.println("Error loading scores from database: " + e.getMessage());
      e.printStackTrace();
    }
  }

  /**
   * Refresh scores from database (call this when new scores are added)
   */
  public void refreshScores() {
    scoresLoaded = false;
    scoreLoadError = false;
    errorMessage = "";
  }

  public String selectMapViaMouse(Point mouseLocation) {
    return mapSelection.selectMap(mouseLocation);
  }

  public String selectMapViaKeyboard(int index) {
    return mapSelection.selectMap(index);
  }

  public int changeSelectedMap(int index, boolean up) {
    return mapSelection.changeSelectedMap(index, up);
  }

  /**
   * Update the panel size when the window is resized
   */
  public void updateSize(int width, int height) {
    setPreferredSize(new Dimension(width, height));
    setMaximumSize(new Dimension(width, height));
    setMinimumSize(new Dimension(width, height));
    revalidate();
    repaint();
  }
}
