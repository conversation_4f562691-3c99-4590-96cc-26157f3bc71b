package model.enemy;

import java.awt.image.BufferedImage;

public interface IEnemyFactory {
  public Enemy createEnemy(double x, double y, BufferedImage style);

  /**
   * Create enemy with specific unique ID (for consistent map loading)
   */
  public default Enemy createEnemy(double x, double y, BufferedImage style, int uniqueId) {
    // Default implementation for backward compatibility
    return createEnemy(x, y, style);
  }
}
