package model.enemy;

import java.awt.image.BufferedImage;

public class Concrete<PERSON>reate<PERSON><PERSON>ba implements IEnemyFactory {
  public Enemy createEnemy(double x, double y, BufferedImage style) {

    return new Goomba(x, y, style);

  }

  /**
   * Create enemy with specific unique ID (for consistent map loading)
   */
  public Enemy createEnemy(double x, double y, BufferedImage style, int uniqueId) {
    return new Goomba(x, y, style, uniqueId);
  }

}
