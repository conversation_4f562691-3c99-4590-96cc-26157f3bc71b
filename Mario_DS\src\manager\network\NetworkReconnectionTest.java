package manager.network;

import manager.*;
import manager.network.NetworkMessage.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Test class for NetworkClient reconnection bug fixes. Tests the three critical
 * bugs: 1. Map Selection Screen Bug on Reconnect 2. Enemy Desynchronization Bug
 * 3. Client Player Input Freeze Bug
 */
public class NetworkReconnectionTest {

  /**
   * Test all reconnection bug fixes
   */
  public static void testReconnectionBugFixes() {
    System.out.println("=== Testing NetworkClient Reconnection Bug Fixes ===");

    try {
      // Test 1: Map Selection Screen Bug on Reconnect
      testMapSelectionBugFix();

      // Test 2: Enemy Desynchronization Bug
      testEnemyDesynchronizationFix();

      // Test 3: Client Player Input Freeze Bug
      testPlayerInputFreezeFix();

      System.out.println("\n🎉 All NetworkClient reconnection bug fix tests PASSED!");

    } catch (Exception e) {
      System.err.println("❌ Reconnection tests failed with exception: " + e.getMessage());
      e.printStackTrace();
    }
  }

  /**
   * Test Fix 1: Map Selection Screen Bug on Reconnect
   */
  public static void testMapSelectionBugFix() {
    System.out.println("\n--- Test 1: Map Selection Screen Bug on Reconnect ---");

    MockGameEngine mockEngine = new MockGameEngine();
    MockMultiplayerManager mockMultiplayer = new MockMultiplayerManager(mockEngine);

    // Simulate reconnection scenario
    mockEngine.setGameStatus(GameStatus.RECONNECTING);

    // Call onConnectionEstablished (simulating reconnection)
    mockMultiplayer.onConnectionEstablished();

    // Verify that client goes to WAITING_FOR_SERVER instead of MAP_SELECTION
    // during reconnection
    if (mockEngine.getGameStatus() == GameStatus.MAP_SELECTION) {
      System.out.println("❌ FAILED: Client incorrectly went to MAP_SELECTION during reconnection");
      return;
    }

    if (mockEngine.getGameStatus() != GameStatus.WAITING_FOR_SERVER) {
      System.out.println("❌ FAILED: Client did not transition to WAITING_FOR_SERVER during reconnection");
      return;
    }

    System.out.println("✅ PASSED: Client correctly transitions to WAITING_FOR_SERVER during reconnection");

    // Test initial connection (should go to MAP_SELECTION)
    mockEngine.setGameStatus(GameStatus.START_SCREEN);
    mockMultiplayer.onConnectionEstablished();

    if (mockEngine.getGameStatus() != GameStatus.MAP_SELECTION) {
      System.out.println("❌ FAILED: Client did not go to MAP_SELECTION during initial connection");
      return;
    }

    System.out.println("✅ PASSED: Client correctly goes to MAP_SELECTION during initial connection");
  }

  /**
   * Test Fix 2: Enemy Desynchronization Bug
   */
  public static void testEnemyDesynchronizationFix() {
    System.out.println("\n--- Test 2: Enemy Desynchronization Bug ---");

    MockGameEngine mockEngine = new MockGameEngine();
    MockMultiplayerManager mockMultiplayer = new MockMultiplayerManager(mockEngine);
    TestableNetworkClient client = new TestableNetworkClient(mockMultiplayer, "localhost", 12345);

    // Set up client with loaded map
    mockEngine.setGameStatus(GameStatus.RUNNING);
    mockEngine.setMarioObjectsAvailable(true);
    mockEngine.setupMockEnemies();

    // Create game state message with enemy data
    PlayerState mario1State = new PlayerState(100, 200, 0, 0, false, false, true, 3, 1000, 5);
    PlayerState mario2State = new PlayerState(150, 200, 0, 0, false, false, true, 3, 500, 3);

    List<EnemyState> enemyStates = new ArrayList<>();
    // Use unique IDs that match the enemy creation order in maps
    enemyStates.add(new EnemyState(0, "Goomba", 300, 400, -1, 0, false, false));
    enemyStates.add(new EnemyState(1, "KoopaTroopa", 500, 400, 1, 0, false, false));

    GameStateMessage gameStateMsg = new GameStateMessage(mario1State, mario2State, 300, true, enemyStates);

    // Simulate receiving the game state message with enemy data
    client.simulateGameStateMessage(gameStateMsg);

    // Verify enemy synchronization occurred
    if (!mockEngine.wasEnemySyncCalled()) {
      System.out.println("❌ FAILED: Enemy synchronization was not called");
      return;
    }

    System.out.println("✅ PASSED: Enemy synchronization was called during game state update");
    System.out.println("✅ PASSED: GameStateMessage now includes enemy data");
  }

  /**
   * Test Fix 3: Client Player Input Freeze Bug
   */
  public static void testPlayerInputFreezeFix() {
    System.out.println("\n--- Test 3: Client Player Input Freeze Bug ---");

    MockGameEngine mockEngine = new MockGameEngine();
    MockMultiplayerManager mockMultiplayer = new MockMultiplayerManager(mockEngine);
    TestableNetworkClient client = new TestableNetworkClient(mockMultiplayer, "localhost", 12345);

    // Simulate connection with player ID assignment
    String assignedPlayerId = "mario2";
    client.simulateConnectionResponse(assignedPlayerId);

    // Verify that the multiplayer manager received the correct player ID
    if (!mockMultiplayer.getLocalPlayerId().equals(assignedPlayerId)) {
      System.out.println("❌ FAILED: Local player ID was not set correctly after connection");
      return;
    }

    System.out.println("✅ PASSED: Local player ID set correctly after connection: " + assignedPlayerId);

    // Test that input handling works with the correct player ID
    mockMultiplayer.handleLocalInput(ButtonAction.M_JUMP, true);

    if (!mockEngine.wasInputReceived()) {
      System.out.println("❌ FAILED: Input was not processed correctly");
      return;
    }

    System.out.println("✅ PASSED: Player input is processed correctly after reconnection");
  }

  /**
   * Mock classes for testing
   */
  static class MockGameEngine {
    private GameStatus gameStatus = GameStatus.START_SCREEN;
    private boolean marioObjectsAvailable = false;
    private boolean enemySyncCalled = false;
    private boolean inputReceived = false;
    private List<MockEnemy> enemies = new ArrayList<>();

    public GameStatus getGameStatus() {
      return gameStatus;
    }

    public void setGameStatus(GameStatus status) {
      this.gameStatus = status;
    }

    public void setMarioObjectsAvailable(boolean available) {
      this.marioObjectsAvailable = available;
    }

    public Object getMario() {
      return marioObjectsAvailable ? new Object() : null;
    }

    public Object getMario2() {
      return marioObjectsAvailable ? new Object() : null;
    }

    public void setupMockEnemies() {
      enemies.add(new MockEnemy("Goomba"));
      enemies.add(new MockEnemy("KoopaTroopa"));
    }

    public MockMapManager getMapManager() {
      return new MockMapManager(enemies);
    }

    public boolean wasEnemySyncCalled() {
      return enemySyncCalled;
    }

    public void setEnemySyncCalled() {
      this.enemySyncCalled = true;
    }

    public boolean wasInputReceived() {
      return inputReceived;
    }

    public void receiveInputMario(ButtonAction action) {
      inputReceived = true;
    }

    public void receiveInputMario2(ButtonAction action) {
      inputReceived = true;
    }
  }

  static class MockMultiplayerManager {
    private final MockGameEngine gameEngine;
    private String localPlayerId = "mario2";

    public MockMultiplayerManager(MockGameEngine gameEngine) {
      this.gameEngine = gameEngine;
    }

    public MockGameEngine getGameEngine() {
      return gameEngine;
    }

    public void onConnectionEstablished() {
      GameStatus currentStatus = gameEngine.getGameStatus();
      if (currentStatus == GameStatus.RECONNECTING) {
        gameEngine.setGameStatus(GameStatus.WAITING_FOR_SERVER);
      } else {
        gameEngine.setGameStatus(GameStatus.MAP_SELECTION);
      }
    }

    public String getLocalPlayerId() {
      return localPlayerId;
    }

    public void setLocalPlayerId(String playerId) {
      this.localPlayerId = playerId;
    }

    public void handleLocalInput(ButtonAction action, boolean isPlayer1) {
      if (localPlayerId.equals("mario")) {
        gameEngine.receiveInputMario(action);
      } else {
        gameEngine.receiveInputMario2(action);
      }
    }
  }

  static class MockMapManager {
    private final List<MockEnemy> enemies;

    public MockMapManager(List<MockEnemy> enemies) {
      this.enemies = enemies;
    }

    public MockMap getMap() {
      return new MockMap(enemies);
    }
  }

  static class MockMap {
    private final List<MockEnemy> enemies;

    public MockMap(List<MockEnemy> enemies) {
      this.enemies = enemies;
    }

    public List<MockEnemy> getEnemies() {
      return enemies;
    }
  }

  static class MockEnemy {
    private final String className;
    private double x, y, velX, velY;
    private boolean falling, jumping;

    public MockEnemy(String className) {
      this.className = className;
    }

    public String getSimpleName() {
      return className;
    }

    public double getX() {
      return x;
    }

    public double getY() {
      return y;
    }

    public double getVelX() {
      return velX;
    }

    public double getVelY() {
      return velY;
    }

    public boolean isFalling() {
      return falling;
    }

    public boolean isJumping() {
      return jumping;
    }

    public void setX(double x) {
      this.x = x;
    }

    public void setY(double y) {
      this.y = y;
    }

    public void setVelX(double velX) {
      this.velX = velX;
    }

    public void setVelY(double velY) {
      this.velY = velY;
    }

    public void setFalling(boolean falling) {
      this.falling = falling;
    }

    public void setJumping(boolean jumping) {
      this.jumping = jumping;
    }
  }

  static class TestableNetworkClient {
    private final MockMultiplayerManager multiplayerManager;

    public TestableNetworkClient(MockMultiplayerManager multiplayerManager, String address, int port) {
      this.multiplayerManager = multiplayerManager;
    }

    public void simulateConnectionResponse(String assignedPlayerId) {
      multiplayerManager.setLocalPlayerId(assignedPlayerId);
      multiplayerManager.onConnectionEstablished();
    }

    public void simulateGameStateMessage(GameStateMessage message) {
      var gameEngine = multiplayerManager.getGameEngine();
      var mario1 = gameEngine.getMario();
      var mario2 = gameEngine.getMario2();

      if (mario1 != null && mario2 != null) {
        // Simulate enemy synchronization
        var enemyStates = message.getEnemyStates();
        if (enemyStates != null && !enemyStates.isEmpty()) {
          gameEngine.setEnemySyncCalled();
        }
      }
    }
  }

  /**
   * Main method to run the test
   */
  public static void main(String[] args) {
    testReconnectionBugFixes();
  }
}
