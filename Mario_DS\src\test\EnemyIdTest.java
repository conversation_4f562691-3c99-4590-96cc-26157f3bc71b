package test;

import model.enemy.*;
import manager.network.NetworkMessage.EnemyState;

/**
 * Test class to verify enemy unique ID functionality
 */
public class EnemyIdTest {

    public static void main(String[] args) {
        testEnemyUniqueIds();
        testEnemyStateWithIds();
        testIdCounterReset();
        System.out.println("All enemy ID tests passed!");
    }

    /**
     * Test that enemies get unique IDs
     */
    public static void testEnemyUniqueIds() {
        System.out.println("Testing enemy unique IDs...");
        
        // Reset counter for consistent testing
        Enemy.resetIdCounter();
        
        // Create enemies using factories
        IEnemyFactory goombaFactory = new ConcreteCreateGoomba();
        IEnemyFactory koopaFactory = new ConcreteCreateKoopaTroopa();
        
        Enemy goomba1 = goombaFactory.createEnemy(100, 200, null, 0);
        Enemy goomba2 = goombaFactory.createEnemy(200, 200, null, 1);
        Enemy koopa1 = koopaFactory.createEnemy(300, 200, null, 2);
        Enemy koopa2 = koopaFactory.createEnemy(400, 200, null, 3);
        
        // Verify unique IDs
        assert goomba1.getUniqueId() == 0 : "Goomba1 should have ID 0, got " + goomba1.getUniqueId();
        assert goomba2.getUniqueId() == 1 : "Goomba2 should have ID 1, got " + goomba2.getUniqueId();
        assert koopa1.getUniqueId() == 2 : "Koopa1 should have ID 2, got " + koopa1.getUniqueId();
        assert koopa2.getUniqueId() == 3 : "Koopa2 should have ID 3, got " + koopa2.getUniqueId();
        
        System.out.println("✓ Enemy unique IDs working correctly");
    }

    /**
     * Test EnemyState creation with unique IDs
     */
    public static void testEnemyStateWithIds() {
        System.out.println("Testing EnemyState with unique IDs...");
        
        Enemy.resetIdCounter();
        IEnemyFactory goombaFactory = new ConcreteCreateGoomba();
        Enemy goomba = goombaFactory.createEnemy(150, 250, null, 5);
        
        // Create EnemyState
        EnemyState state = new EnemyState(
            goomba.getUniqueId(),
            goomba.getClass().getSimpleName(),
            goomba.getX(),
            goomba.getY(),
            goomba.getVelX(),
            goomba.getVelY(),
            goomba.isFalling(),
            goomba.isJumping()
        );
        
        // Verify state
        assert state.getEnemyId() == 5 : "EnemyState should have ID 5, got " + state.getEnemyId();
        assert state.getEnemyType().equals("Goomba") : "EnemyState should be Goomba, got " + state.getEnemyType();
        assert state.getX() == 150.0 : "EnemyState X should be 150, got " + state.getX();
        assert state.getY() == 250.0 : "EnemyState Y should be 250, got " + state.getY();
        
        System.out.println("✓ EnemyState with unique IDs working correctly");
    }

    /**
     * Test ID counter reset functionality
     */
    public static void testIdCounterReset() {
        System.out.println("Testing ID counter reset...");
        
        // Create some enemies
        IEnemyFactory factory = new ConcreteCreateGoomba();
        Enemy enemy1 = factory.createEnemy(0, 0, null);
        Enemy enemy2 = factory.createEnemy(0, 0, null);
        
        int id1 = enemy1.getUniqueId();
        int id2 = enemy2.getUniqueId();
        
        // Reset counter
        Enemy.resetIdCounter();
        
        // Create new enemies
        Enemy enemy3 = factory.createEnemy(0, 0, null);
        Enemy enemy4 = factory.createEnemy(0, 0, null);
        
        int id3 = enemy3.getUniqueId();
        int id4 = enemy4.getUniqueId();
        
        // Verify reset worked
        assert id3 == 0 : "After reset, first enemy should have ID 0, got " + id3;
        assert id4 == 1 : "After reset, second enemy should have ID 1, got " + id4;
        assert id1 != id3 || id2 != id4 : "IDs should be different after reset";
        
        System.out.println("✓ ID counter reset working correctly");
    }
}
