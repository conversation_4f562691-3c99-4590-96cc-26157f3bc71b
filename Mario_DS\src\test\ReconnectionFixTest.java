package test;

import manager.GameStatus;
import manager.network.NetworkMessage.*;
import model.enemy.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Test class to verify reconnection fixes and enemy synchronization with unique IDs
 */
public class ReconnectionFixTest {

    public static void main(String[] args) {
        testEnemySyncWithUniqueIds();
        testReconnectionStateTransition();
        testViewportCullingWithUniqueIds();
        System.out.println("All reconnection fix tests passed!");
    }

    /**
     * Test enemy synchronization using unique IDs instead of array indices
     */
    public static void testEnemySyncWithUniqueIds() {
        System.out.println("Testing enemy sync with unique IDs...");
        
        // Reset enemy ID counter
        Enemy.resetIdCounter();
        
        // Create local enemies (client side)
        List<Enemy> localEnemies = new ArrayList<>();
        IEnemyFactory goombaFactory = new ConcreteCreateGoomba();
        IEnemyFactory koopaFactory = new ConcreteCreateKoopaTroopa();
        
        localEnemies.add(goombaFactory.createEnemy(100, 200, null, 0));  // ID 0
        localEnemies.add(koopaFactory.createEnemy(200, 200, null, 1));   // ID 1
        localEnemies.add(goombaFactory.createEnemy(300, 200, null, 2));  // ID 2
        
        // Create server enemy states (simulating viewport culling - only some enemies sent)
        List<EnemyState> serverStates = new ArrayList<>();
        serverStates.add(new EnemyState(1, "KoopaTroopa", 250, 220, -2, 0, false, false)); // ID 1
        serverStates.add(new EnemyState(2, "Goomba", 350, 210, 1, 0, false, false));       // ID 2
        // Note: Enemy ID 0 is not sent (viewport culled)
        
        // Simulate client-side enemy synchronization
        for (EnemyState serverState : serverStates) {
            int enemyUniqueId = serverState.getEnemyId();
            
            // Find matching local enemy by unique ID
            Enemy matchingEnemy = null;
            for (Enemy enemy : localEnemies) {
                if (enemy != null && enemy.getUniqueId() == enemyUniqueId) {
                    matchingEnemy = enemy;
                    break;
                }
            }
            
            if (matchingEnemy != null) {
                // Sync enemy state
                matchingEnemy.setX(serverState.getX());
                matchingEnemy.setY(serverState.getY());
                matchingEnemy.setVelX(serverState.getVelX());
                matchingEnemy.setVelY(serverState.getVelY());
                
                System.out.println("Synced enemy " + enemyUniqueId + " (" + serverState.getEnemyType() + 
                                 ") to position (" + serverState.getX() + "," + serverState.getY() + ")");
            }
        }
        
        // Verify synchronization
        assert localEnemies.get(0).getX() == 100.0 : "Enemy 0 should not be synced (not in server states)";
        assert localEnemies.get(1).getX() == 250.0 : "Enemy 1 should be synced to X=250";
        assert localEnemies.get(1).getY() == 220.0 : "Enemy 1 should be synced to Y=220";
        assert localEnemies.get(2).getX() == 350.0 : "Enemy 2 should be synced to X=350";
        assert localEnemies.get(2).getY() == 210.0 : "Enemy 2 should be synced to Y=210";
        
        System.out.println("✓ Enemy sync with unique IDs working correctly");
    }

    /**
     * Test reconnection state transition logic
     */
    public static void testReconnectionStateTransition() {
        System.out.println("Testing reconnection state transition...");
        
        // Simulate the state transition logic
        GameStatus currentStatus = GameStatus.RECONNECTING;
        GameStatus serverStatus = GameStatus.RUNNING;
        
        // This simulates the logic in NetworkClient.handleStatusSync()
        if (currentStatus == GameStatus.RECONNECTING) {
            if (serverStatus == GameStatus.RUNNING) {
                // Should transition to WAITING_FOR_SERVER first
                currentStatus = GameStatus.WAITING_FOR_SERVER;
                System.out.println("Transitioned from RECONNECTING to WAITING_FOR_SERVER");
                
                // Then after map loading, should transition to RUNNING
                // (This would happen in handleGameStateUpdate when Mario objects are available)
                currentStatus = GameStatus.RUNNING;
                System.out.println("Transitioned from WAITING_FOR_SERVER to RUNNING");
            }
        }
        
        assert currentStatus == GameStatus.RUNNING : "Should end up in RUNNING state, got " + currentStatus;
        
        System.out.println("✓ Reconnection state transition working correctly");
    }

    /**
     * Test that viewport culling doesn't break enemy sync with unique IDs
     */
    public static void testViewportCullingWithUniqueIds() {
        System.out.println("Testing viewport culling with unique IDs...");
        
        Enemy.resetIdCounter();
        
        // Create many enemies (simulating a large map)
        List<Enemy> allEnemies = new ArrayList<>();
        IEnemyFactory factory = new ConcreteCreateGoomba();
        
        for (int i = 0; i < 20; i++) {
            allEnemies.add(factory.createEnemy(i * 100, 200, null, i));
        }
        
        // Simulate server sending only visible enemies (viewport culled)
        List<EnemyState> visibleEnemyStates = new ArrayList<>();
        // Only enemies 5, 7, 12, 15 are visible
        visibleEnemyStates.add(new EnemyState(5, "Goomba", 550, 210, -1, 0, false, false));
        visibleEnemyStates.add(new EnemyState(7, "Goomba", 750, 220, 1, 0, false, false));
        visibleEnemyStates.add(new EnemyState(12, "Goomba", 1250, 200, -1, 0, false, false));
        visibleEnemyStates.add(new EnemyState(15, "Goomba", 1550, 190, 1, 0, false, false));
        
        // Simulate client synchronization
        int syncedCount = 0;
        for (EnemyState serverState : visibleEnemyStates) {
            int enemyUniqueId = serverState.getEnemyId();
            
            // Find matching enemy by unique ID
            Enemy matchingEnemy = null;
            for (Enemy enemy : allEnemies) {
                if (enemy != null && enemy.getUniqueId() == enemyUniqueId) {
                    matchingEnemy = enemy;
                    break;
                }
            }
            
            if (matchingEnemy != null) {
                matchingEnemy.setX(serverState.getX());
                matchingEnemy.setY(serverState.getY());
                syncedCount++;
            }
        }
        
        // Verify that all visible enemies were synced correctly
        assert syncedCount == 4 : "Should have synced 4 enemies, synced " + syncedCount;
        assert allEnemies.get(5).getX() == 550.0 : "Enemy 5 should be synced";
        assert allEnemies.get(7).getX() == 750.0 : "Enemy 7 should be synced";
        assert allEnemies.get(12).getX() == 1250.0 : "Enemy 12 should be synced";
        assert allEnemies.get(15).getX() == 1550.0 : "Enemy 15 should be synced";
        
        // Verify that non-visible enemies were not affected
        assert allEnemies.get(0).getX() == 0.0 : "Enemy 0 should not be synced";
        assert allEnemies.get(10).getX() == 1000.0 : "Enemy 10 should not be synced";
        
        System.out.println("✓ Viewport culling with unique IDs working correctly");
    }
}
